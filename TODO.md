# TODO List

## UI Components

- [x] expandable menu
- [x] style expandable menu
- [x] fix close menu button needs to be clicked twice
- [x] move close menu button to its own component
- [x] the options on the expandable menu should be a separated component
- [x] components in folders
- [x] improve UI
- [ ] tips as carousel instead of article
- [x] tips list screen
- [ ] start screen how to use the app
- [ ] theme modal close button should be a component
- [x] animations

## Theming

- [x] options to switch themes
- [x] theme option should be a component
- [x] dark theme
- [x] neon theme
- [x] dark neon theme
- [ ] light neon theme
- [ ] dark synthwave theme
- [ ] light synthwave theme
- [ ] save theme preference to db

## Tip Content

- [x] rename tip.body to tip.subtitle
- [x] style subtitle
- [x] move tip title
- [x] move tip body
- [x] move tip url
- [x] src\components\Tip.tsx settip should be a single function
- [x] update TipWithDetail type
- [ ] add summary to all tips
- [x] show studies summary
- [ ] add graph to all tips
- [ ] show graphs
- [ ] rate tips (like, not like, wrong info, broken link)

## Database

- [x] sqlite persistent
- [ ] delete db if exists
- [ ] seed database script should also create the .db file
- [ ] check why the db isnt updating after running seed and npm run android
- [ ] add graphs to db
- [ ] add studies summary to db
- [x] table for details.json
- [x] another json for the study summary, link, and graph
- [ ] move database out of repository
- [ ] shorter tips summaries like bullet points for carousel
- [ ] remove unused files, functions in src\database\tips

## Images

- [x] images
- [x] add images to tips
- [x] generate images for each tip
- [x] fix getImageSource function
- [x] move images to images hosting
- [ ] move image after resizing and compressing
- [ ] move images to image storage
- [ ] better photos
- [ ] tip thumbnail for all tips screen
- [ ] script to get images from hosting

## Scripts

- [x] add script to resize images
- [x] add script to compress images
- [x] scripts to populate the database
- [ ] script to rename image with uuid
- [ ] scripts rename, resize and compress should run one after another
- [ ] colored console logs for scripts
- [ ] readme for each script
- [ ] scripts should have their own package.json

## Code Quality & Organization

- [ ] create file for constants
- [ ] move to constants const database_name = 'TipsDatabase.db';
- [ ] move to constantsexport enum ScreenNames
- [ ] menu options should be on constants
- [ ] available themes should be on constants
- [ ] space in imports
- [ ] eslint project
- [ ] prettier project
- [ ] add test config
- [ ] tipcard in TipListItem should be a separated component
- [ ] ImagePlaceholder in TipListItem should be a separated component

## App Identity

- [ ] app name
- [ ] logo
- [ ] disclaimer this is not a medic

## Features

- [ ] send comments, doubts, complains
- [ ] send notification on daily tip
- [ ] ads

## Deployment

- [ ] publish to app store
- [ ] install on phone
