import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
} from 'react';
import {View, StyleSheet, Linking, Animated, Easing} from 'react-native';
import Colors from '../../theme/colors';
import {useTheme} from '../../theme/ThemeContext';
import {getFirstTip} from '../../database/tips/getFirstTip';
import {getNextTip} from '../../database/tips/getNextTip';
import {getPreviousTip} from '../../database/tips/getPreviousTip';
import {getTipById} from '../../database/tips/getTipById';
import TipImg from './TipImg';
import TipSubtitle from './TipSubtitle';
import TipTitle from './TipTitle';
import TipUrl from './TipUrl';
import TipSummary from './TipSummary';
import {TipWithDetails} from '../../models/types';

interface TipProps {
  onNext: () => void;
  initialTipId?: number | null;
}

export interface TipRef {
  handleNext: () => void;
  handlePrevious: () => void;
}

const Tip = forwardRef<TipRef, TipProps>(({onNext, initialTipId}, ref) => {
  // Use TipWithDetails for state
  const [tip, setTip] = useState<TipWithDetails | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const {isDarkMode, isNeonMode} = useTheme();

  // Animation values
  const slideAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (initialTipId) {
      // If initialTipId is provided, fetch that specific tip
      getTipById(initialTipId, specificTip => {
        if (specificTip) {
          setTip(specificTip);
        } else {
          // Fallback to first tip if the specific tip is not found
          getFirstTip(firstTip => {
            setTip(firstTip);
          });
        }
      });
    } else {
      // Otherwise, get the first tip as usual
      getFirstTip(firstTip => {
        setTip(firstTip);
      });
    }
  }, [initialTipId]);

  // Animation helper function
  const animateTransition = (
    direction: 'next' | 'previous',
    newTip: TipWithDetails,
  ) => {
    if (isAnimating) return;

    setIsAnimating(true);
    const slideDirection = direction === 'next' ? 300 : -300;

    // Slide out current tip
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -slideDirection,
        duration: 200,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Update tip and reset position for slide in
      setTip(newTip);
      slideAnim.setValue(slideDirection);

      // Slide in new tip
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 250,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setIsAnimating(false);
      });
    });
  };

  const handleNext = () => {
    if (tip && !isAnimating) {
      // getNextTip callback now provides TipWithDetails | null
      getNextTip(tip.id, nextTip => {
        if (nextTip) {
          console.log('nextTip', nextTip);
          animateTransition('next', nextTip);
        }
        // If nextTip is null (end of list reached, wrapped around), getNextTip handles it
      });
    }
  };

  const handlePrevious = () => {
    if (tip && !isAnimating) {
      // getPreviousTip callback now provides TipWithDetails | null
      getPreviousTip(tip.id, previousTip => {
        if (previousTip) {
          animateTransition('previous', previousTip);
        }
        // If previousTip is null (start of list reached, wrapped around), getPreviousTip handles it
      });
    }
  };

  const handleOpenUrl = (url: string) => {
    if (url) {
      console.log('url', url);
      Linking.openURL(url);
    }
  };

  useImperativeHandle(ref, () => ({
    handleNext,
    handlePrevious,
  }));

  useEffect(() => {
    if (onNext) {
      onNext();
    }
  }, [tip, onNext]); // Render logic using the tip state (TipWithDetails)
  if (!tip) {
    // Return loading indicator
    let loadingBorderColor;

    if (isNeonMode) {
      loadingBorderColor = Colors.neon.border;
    } else {
      loadingBorderColor = isDarkMode
        ? Colors.dark.border
        : Colors.light.border;
    }

    return (
      <View
        style={[
          styles.container,
          styles.loadingContainer,
          {
            borderColor: loadingBorderColor,
            ...(isNeonMode && {
              shadowColor: Colors.neon.glow.blue,
              shadowOffset: {width: 0, height: 0},
              shadowOpacity: 0.8,
              shadowRadius: 10,
              elevation: 10,
            }),
          },
        ]}>
        <View style={styles.loadingPlaceholder} />
        <View style={[styles.loadingText, styles.loadingTextMedium]} />
        <View style={[styles.loadingText, styles.loadingTextLarge]} />
        <View style={[styles.loadingText, styles.loadingTextSmall]} />
      </View>
    );
  }

  // Get colors based on theme
  let backgroundColor, borderColor;

  if (isNeonMode) {
    backgroundColor = Colors.neon.cardBg;
    borderColor = Colors.neon.border;
  } else {
    backgroundColor = isDarkMode ? Colors.dark.cardBg : Colors.light.cardBg;
    borderColor = isDarkMode ? Colors.dark.border : Colors.light.border;
  }

  // Get border top color for details container
  let borderTopColor;
  if (isNeonMode) {
    borderTopColor = Colors.neon.border;
  } else {
    borderTopColor = isDarkMode ? Colors.dark.border : Colors.light.border;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor,
          borderColor,
          opacity: opacityAnim,
          transform: [{translateX: slideAnim}],
          ...(isNeonMode && {
            shadowColor: Colors.neon.glow.blue,
            shadowOffset: {width: 0, height: 0},
            shadowOpacity: 0.8,
            shadowRadius: 10,
            elevation: 10,
          }),
        },
      ]}>
      <TipTitle title={tip.title} />
      <TipImg img={tip.img} />
      <TipSubtitle subtitle={tip.subtitle} />
      <TipUrl url={tip.url} onPress={() => handleOpenUrl(tip.url)} />
      {/* Removed tip.details check from here */}
      <View
        style={[
          styles.detailsContainer,
          {
            borderTopColor,
          },
        ]}>
        {/* TipSummary now handles null summary internally */}
        <TipSummary summary={tip.details?.summary} />
        {/* Display graph data or link if applicable */}
        {/* <TipBody body={`Graph: ${tip.details.graph}`} /> */}
        {/* TipUrl now handles null url internally */}
        <TipUrl
          url={tip.details?.url}
          onPress={() => tip.details?.url && handleOpenUrl(tip.details.url)}
        />
      </View>
    </Animated.View>
  );
});

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
    marginTop: 10,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  detailsContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
  },
  loadingContainer: {
    minHeight: 250,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 20,
  },
  loadingText: {
    height: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  loadingTextSmall: {
    width: '70%',
  },
  loadingTextMedium: {
    width: '80%',
  },
  loadingTextLarge: {
    width: '90%',
  },
});

export default Tip;
